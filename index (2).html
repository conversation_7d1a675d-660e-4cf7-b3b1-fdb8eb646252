<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>scTimer - Coming Soon</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&family=Roboto+Mono:wght@400;700&display=swap" rel="stylesheet">
    <style>
        /* ------------------- */
        /* --- CSS STYLING --- */
        /* ------------------- */

        :root {
            --color-bg: #121212;
            --color-primary: #ffffff;
            --color-secondary: #aaaaaa;
            --color-accent: #00e099;
            --color-accent-dark: #00b37a;

            /* Cube Colors for Background Animation */
            --cube-white: #ffffff;
            --cube-yellow: #ffd500;
            --cube-blue: #0045ad;
            --cube-green: #009b48;
            --cube-red: #b90000;
            --cube-orange: #ff5900;
        }

        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html, body {
            height: 100%;
            overflow: hidden;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--color-bg);
            color: var(--color-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 1rem;
        }

        .container {
            position: relative;
            z-index: 2;
            max-width: 800px;
            width: 100%;
            animation: fadeIn 2s ease-in-out;
        }

        /* --- BACKGROUND ANIMATION --- */
        .background-cubes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            overflow: hidden;
        }

        .cube {
            position: absolute;
            bottom: -150px;
            background-color: rgba(255, 255, 255, 0.05);
            animation: rise 25s infinite linear;
            border-radius: 8px;
        }

        .cube:nth-child(1) { left: 10%; width: 80px; height: 80px; animation-duration: 22s; animation-delay: 0s; background-color: rgba(0, 69, 173, 0.2); } /* Blue */
        .cube:nth-child(2) { left: 20%; width: 30px; height: 30px; animation-duration: 18s; animation-delay: 2s; background-color: rgba(255, 213, 0, 0.2); } /* Yellow */
        .cube:nth-child(3) { left: 25%; width: 50px; height: 50px; animation-duration: 28s; animation-delay: 4s; background-color: rgba(255, 255, 255, 0.2); } /* White */
        .cube:nth-child(4) { left: 40%; width: 60px; height: 60px; animation-duration: 20s; animation-delay: 0s; background-color: rgba(0, 155, 72, 0.2); } /* Green */
        .cube:nth-child(5) { left: 65%; width: 20px; height: 20px; animation-duration: 16s; animation-delay: 1s; background-color: rgba(185, 0, 0, 0.2); } /* Red */
        .cube:nth-child(6) { left: 75%; width: 110px; height: 110px; animation-duration: 35s; animation-delay: 3s; background-color: rgba(255, 89, 0, 0.2); } /* Orange */
        .cube:nth-child(7) { left: 90%; width: 45px; height: 45px; animation-duration: 25s; animation-delay: 5s; background-color: rgba(0, 69, 173, 0.2); } /* Blue */
        .cube:nth-child(8) { left: 50%; width: 90px; height: 90px; animation-duration: 30s; animation-delay: 6s; background-color: rgba(255, 255, 255, 0.2); } /* White */
        .cube:nth-child(9) { left: 80%; width: 25px; height: 25px; animation-duration: 15s; animation-delay: 8s; background-color: rgba(255, 213, 0, 0.2); } /* Yellow */
        .cube:nth-child(10) { left: 5%; width: 40px; height: 40px; animation-duration: 23s; animation-delay: 10s; background-color: rgba(0, 155, 72, 0.2); } /* Green */
        
        @keyframes rise {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            100% {
                transform: translateY(-120vh) rotate(720deg);
            }
        }

        /* --- HEADER --- */
        header h1 {
            font-family: 'Roboto Mono', monospace;
            font-size: clamp(2.5rem, 8vw, 4rem);
            font-weight: 700;
            letter-spacing: -2px;
            animation: textFlicker 3s infinite alternate;
        }

        header .domain {
            font-size: clamp(1rem, 3vw, 1.25rem);
            color: var(--color-secondary);
            margin-top: -10px;
            letter-spacing: 2px;
        }

        @keyframes textFlicker {
            0%, 18%, 22%, 25%, 53%, 57%, 100% {
                text-shadow:
                    0 0 4px #fff,
                    0 0 11px #fff,
                    0 0 19px #fff,
                    0 0 40px var(--color-accent),
                    0 0 80px var(--color-accent),
                    0 0 90px var(--color-accent),
                    0 0 100px var(--color-accent),
                    0 0 150px var(--color-accent);
            }
            20%, 24%, 55% {       
                text-shadow: none;
            }
        }
        
        /* --- MAIN CONTENT --- */
        main {
            margin-top: 2rem;
        }

        main h2 {
            font-size: clamp(1.2rem, 4vw, 2rem);
            font-weight: 300;
            color: var(--color-secondary);
        }

        main h3 {
            font-size: clamp(2rem, 6vw, 3.5rem);
            font-weight: 700;
            color: var(--color-primary);
            text-transform: uppercase;
            letter-spacing: 4px;
            margin-bottom: 2rem;
        }

        /* --- FEATURE CAROUSEL --- */
        .feature-carousel {
            font-family: 'Roboto Mono', monospace;
            font-size: clamp(1rem, 3vw, 1.5rem);
            height: 3rem;
            line-height: 3rem;
            color: var(--color-secondary);
            margin-bottom: 2rem;
        }
        .feature-carousel .typed-text {
            color: var(--color-accent);
            font-weight: 700;
        }
        .typed-text .cursor {
            display: inline-block;
            background-color: var(--color-accent);
            width: 3px;
            margin-left: 5px;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }

        /* --- COUNTDOWN --- */
        #countdown {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
        }

        .countdown-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 1rem;
            min-width: 90px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .countdown-item span {
            font-family: 'Roboto Mono', monospace;
            font-size: clamp(2rem, 6vw, 3rem);
            font-weight: 700;
            color: var(--color-primary);
        }

        .countdown-item p {
            font-size: 0.8rem;
            text-transform: uppercase;
            color: var(--color-secondary);
            letter-spacing: 1px;
        }
        
        /* --- SUBSCRIBE FORM --- */
        .subscribe-text {
            color: var(--color-secondary);
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .subscribe-form {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .subscribe-form input {
            font-family: 'Poppins', sans-serif;
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--color-primary);
            padding: 0.8rem 1rem;
            border-radius: 8px;
            font-size: 1rem;
            width: 100%;
            max-width: 300px;
            transition: all 0.3s ease;
        }

        .subscribe-form input::placeholder {
            color: var(--color-secondary);
        }

        .subscribe-form input:focus {
            outline: none;
            border-color: var(--color-accent);
            box-shadow: 0 0 15px rgba(0, 224, 153, 0.3);
        }

        .subscribe-form button {
            font-family: 'Poppins', sans-serif;
            background-color: var(--color-accent);
            color: var(--color-bg);
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .subscribe-form button:hover {
            background-color: var(--color-accent-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 224, 153, 0.2);
        }
        
        /* --- FOOTER --- */
        footer {
            margin-top: 3rem;
            color: var(--color-secondary);
            font-size: 0.9rem;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* --- RESPONSIVE DESIGN --- */
        @media (max-width: 480px) {
            .countdown-item {
                min-width: 70px;
                padding: 0.75rem;
            }
            .subscribe-form {
                flex-direction: column;
                align-items: center;
            }
            .subscribe-form input, .subscribe-form button {
                width: 100%;
                max-width: 300px;
            }
        }

    </style>
</head>
<body>

    <div class="background-cubes">
        <div class="cube"></div>
        <div class="cube"></div>
        <div class="cube"></div>
        <div class="cube"></div>
        <div class="cube"></div>
        <div class="cube"></div>
        <div class="cube"></div>
        <div class="cube"></div>
        <div class="cube"></div>
        <div class="cube"></div>
    </div>

    <div class="container">
        <header>
            <h1>scTimer 🧩</h1>
            <p class="domain">sctimer.com</p>
        </header>

        <main>
            <h2>The Ultimate Speedcubing Timer is</h2>
            <h3>Launching Soon</h3>

            <div class="feature-carousel">
                <span>Featuring </span><span id="feature-text" class="typed-text"></span>
            </div>

            <div id="countdown">
                <div class="countdown-item">
                    <span id="days">00</span>
                    <p>Days</p>
                </div>
                <div class="countdown-item">
                    <span id="hours">00</span>
                    <p>Hours</p>
                </div>
                <div class="countdown-item">
                    <span id="minutes">00</span>
                    <p>Minutes</p>
                </div>
                <div class="countdown-item">
                    <span id="seconds">00</span>
                    <p>Seconds</p>
                </div>
            </div>

            <p class="subscribe-text">Be the first to know when we launch.</p>
            <form class="subscribe-form" onsubmit="event.preventDefault(); alert('Thank you for subscribing!');">
                <input type="email" placeholder="Enter your email" required>
                <button type="submit">Notify Me</button>
            </form>

        </main>
        <footer>
            <p>&copy; 2025 scTimer. All Rights Reserved.</p>
        </footer>
    </div>

    <script>
    /* ---------------------- */
    /* --- JAVASCRIPT LOGIC --- */
    /* ---------------------- */
    
    document.addEventListener('DOMContentLoaded', () => {

        // --- COUNTDOWN TIMER LOGIC ---
        const countdown = () => {
            const launchDate = new Date('October 1, 2025 00:00:00').getTime();
            const now = new Date().getTime();
            const distance = launchDate - now;

            if (distance < 0) {
                document.getElementById('countdown').innerHTML = "<h2>Launched!</h2>";
                return;
            }

            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            document.getElementById('days').innerText = String(days).padStart(2, '0');
            document.getElementById('hours').innerText = String(hours).padStart(2, '0');
            document.getElementById('minutes').innerText = String(minutes).padStart(2, '0');
            document.getElementById('seconds').innerText = String(seconds).padStart(2, '0');
        };

        setInterval(countdown, 1000);
        countdown(); // Initial call


        // --- TYPING ANIMATION FOR FEATURES ---
        const typingFeature = () => {
            const features = [
                "Stackmat Integration",
                "Advanced Statistics",
                "Progressive Web App (PWA)",
                "Full WCA Puzzle Support",
                "2D Puzzle Visualization",
                "15+ Languages with RTL",
                "Offline Functionality",
                "Customizable Themes",
                "Solve History & Export"
            ];
            const textElement = document.getElementById('feature-text');
            let featureIndex = 0;
            let charIndex = 0;
            let isDeleting = false;
            const typingSpeed = 100;
            const deletingSpeed = 50;
            const pauseTime = 1500;

            function type() {
                const currentFeature = features[featureIndex];
                let displayText = '';

                if (isDeleting) {
                    displayText = currentFeature.substring(0, charIndex - 1);
                    charIndex--;
                } else {
                    displayText = currentFeature.substring(0, charIndex + 1);
                    charIndex++;
                }

                textElement.innerHTML = `${displayText}<span class="cursor">&nbsp;</span>`;

                let typeSpeed = isDeleting ? deletingSpeed : typingSpeed;

                if (!isDeleting && charIndex === currentFeature.length) {
                    isDeleting = true;
                    typeSpeed = pauseTime;
                } else if (isDeleting && charIndex === 0) {
                    isDeleting = false;
                    featureIndex = (featureIndex + 1) % features.length;
                    typeSpeed = 500;
                }

                setTimeout(type, typeSpeed);
            }
            
            type();
        };

        typingFeature();

    });
    </script>
</body>
</html>