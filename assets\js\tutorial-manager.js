/**
 * Tutorial Manager for scTimer
 * Handles the interactive tutorial system for first-time users
 */

class TutorialManager {
  constructor() {
    this.currentStep = 0;
    this.isActive = false;
    this.hasSeenTutorial = false;
    this.currentLanguage = this.getCurrentLanguage();

    // Tutorial steps configuration
    this.steps = [
      {
        target: "#scramble",
        title: "Scramble Display",
        text: "This shows the scramble sequence for your current puzzle. Each scramble is randomly generated following WCA standards.",
        position: "bottom",
      },
      {
        target: "#timer",
        title: "Timer Controls",
        text: "Press and hold SPACEBAR to start timing, release to begin solving. On mobile, tap and hold the timer area. The timer follows WCA inspection standards.",
        position: "bottom",
      },
      {
        target: "#event-selector-btn",
        title: "Event Selector",
        text: "Choose from all WCA events including 3x3x3, 2x2x2, 4x4x4, and many more puzzle types. Click or tap to open the dropdown menu.",
        position: "bottom",
      },
      {
        target: "#stats-container",
        title: "Statistics Tracking",
        text: "Track your progress with detailed statistics including best time, averages of 5, 12, and 100 solves. Click on any stat to see more details.",
        position: "top",
      },
      {
        target: "#new-scramble",
        title: "Generate New Scramble",
        text: "Generate a new scramble when you're ready for your next solve. Keyboard shortcut: Press N or click the shuffle icon.",
        position: "left",
      },
      {
        target: "#settings-btn",
        title: "Settings & Customization",
        text: "Customize your timer experience with inspection time, sound options, timer modes, and display preferences. Keyboard shortcut: Press S.",
        position: "left",
      },
      {
        target: "body",
        title: "Keyboard Shortcuts",
        text: "Master these shortcuts: SPACEBAR (start/stop timer), G (generate scramble), L (times list), S (settings), E (edit scramble), C (copy scramble), V (toggle visualization), B (toggle statistics), A (more statistics), D (dark mode), I (inspection), ESC (cancel/close), CTRL+Q (empty session), CTRL+X (export session). On mobile, use swipe gestures!",
        position: "center",
      },
      {
        target: "body",
        title: "Mobile Gestures",
        text: "On mobile devices: Swipe left to open times panel, swipe right to close it, tap and hold timer to start, double-tap scramble to copy it. Pinch to zoom on visualizations.",
        position: "center",
      },
      {
        target: "body",
        title: "Pro Tips & Features",
        text: "Enable inspection time in settings for WCA practice. Use different sessions to track various events. Export your times for analysis. The timer works offline as a PWA!",
        position: "center",
      },
    ];

    this.init();
  }

  init() {
    this.checkFirstVisit();
    this.bindEvents();
    this.initializeLanguageSelector();
    this.registerForLanguageChanges();
  }

  registerForLanguageChanges() {
    // Register this tutorial manager globally so language changes can trigger updates
    window.tutorialManager = this;

    // Listen for custom language change events
    document.addEventListener("languageChanged", (event) => {
      this.currentLanguage = event.detail.language;
      this.updateTutorialContent();
    });
  }

  getCurrentLanguage() {
    // Get current language from the language manager or default to English
    return (
      window.currentLanguage || localStorage.getItem("scTimer-language") || "en"
    );
  }

  initializeLanguageSelector() {
    const languageSelect = document.getElementById("tutorial-language-select");
    if (languageSelect) {
      // Set current language as selected
      languageSelect.value = this.currentLanguage;

      // Add event listener for language changes
      languageSelect.addEventListener("change", async (e) => {
        await this.changeLanguage(e.target.value);
      });
    }
  }

  async changeLanguage(newLanguage) {
    this.currentLanguage = newLanguage;

    // Update the main app language using the global changeLanguage function
    if (window.changeLanguage && typeof window.changeLanguage === "function") {
      await window.changeLanguage(newLanguage);
    } else {
      // Fallback: save to localStorage and update manually
      localStorage.setItem("scTimer-language", newLanguage);
      window.currentLanguage = newLanguage;
    }

    // Update tutorial content immediately
    this.updateTutorialContent();
  }

  updateTutorialContent() {
    // Update welcome modal content
    this.updateWelcomeModalContent();

    // Update current step content if tutorial is active
    if (this.isActive) {
      this.updateCurrentStepContent();
    }
  }

  updateWelcomeModalContent() {
    const translations = this.getTranslations();
    if (!translations || !translations.tutorial) return;

    // Update welcome modal elements with correct selectors
    const elements = [
      {
        selector: ".tutorial-welcome-title",
        text: translations.tutorial.welcomeTitle,
      },
      {
        selector: ".tutorial-welcome-subtitle",
        text: translations.tutorial.welcomeSubtitle,
      },
      {
        selector: ".tutorial-language-label span",
        text: translations.tutorial.selectLanguage,
      },
      {
        selector: "[data-i18n='tutorial.feature1']",
        text: translations.tutorial.feature1,
      },
      {
        selector: "[data-i18n='tutorial.feature2']",
        text: translations.tutorial.feature2,
      },
      {
        selector: "[data-i18n='tutorial.feature3']",
        text: translations.tutorial.feature3,
      },
      {
        selector: "[data-i18n='tutorial.feature4']",
        text: translations.tutorial.feature4,
      },
      {
        selector: ".tutorial-welcome-description",
        text: translations.tutorial.welcomeDescription,
      },
      {
        selector: "#tutorial-skip-btn span",
        text: translations.tutorial.skipTutorial,
      },
      {
        selector: "#tutorial-start-btn span",
        text: translations.tutorial.startTour,
      },
    ];

    elements.forEach(({ selector, text }) => {
      const element = document.querySelector(selector);
      if (element) {
        element.textContent = text;
      }
    });
  }

  updateCurrentStepContent() {
    const translations = this.getTranslations();
    if (!translations || !translations.tutorial) return;

    const stepKey = `step${this.currentStep + 1}`;
    const stepTranslation = translations.tutorial[stepKey];

    if (stepTranslation) {
      const titleEl = document.getElementById("tutorial-tooltip-title");
      const textEl = document.getElementById("tutorial-tooltip-text");

      if (titleEl) titleEl.textContent = stepTranslation.title;
      if (textEl) textEl.textContent = stepTranslation.text;
    }

    // Update navigation buttons
    this.updateNavigationButtons();
  }

  getTranslations() {
    // Try to get translations from the global i18nModule (set by language manager)
    if (window.i18nModule && window.i18nModule.translations) {
      return window.i18nModule.translations;
    }

    // Try to get translations from the global getTranslations function
    if (
      window.getTranslations &&
      typeof window.getTranslations === "function"
    ) {
      return window.getTranslations();
    }

    // Try to get translations from the language cache or window object
    if (window.languageCache && window.languageCache[this.currentLanguage]) {
      return window.languageCache[this.currentLanguage];
    }

    if (window.translations && window.translations[this.currentLanguage]) {
      return window.translations[this.currentLanguage];
    }

    // Fallback to English if current language not available
    if (window.languageCache && window.languageCache.en) {
      return window.languageCache.en;
    }

    if (window.translations && window.translations.en) {
      return window.translations.en;
    }

    return null;
  }

  checkFirstVisit() {
    // Check if user has seen the tutorial before
    this.hasSeenTutorial =
      localStorage.getItem("scTimer_tutorial_completed") === "true";

    if (!this.hasSeenTutorial) {
      // Show welcome modal after a short delay
      setTimeout(() => {
        this.showWelcomeModal();
      }, 1000);
    }
  }

  bindEvents() {
    // Welcome modal events
    const startBtn = document.getElementById("tutorial-start-btn");
    const skipBtn = document.getElementById("tutorial-skip-btn");

    if (startBtn) {
      startBtn.addEventListener("click", () => {
        this.hideWelcomeModal();
        this.startTutorial();
      });
    }

    if (skipBtn) {
      skipBtn.addEventListener("click", () => {
        this.skipTutorial();
      });
    }

    // Tutorial navigation events
    const nextBtn = document.getElementById("tutorial-next-btn");
    const prevBtn = document.getElementById("tutorial-prev-btn");
    const closeBtn = document.getElementById("tutorial-close-btn");

    if (nextBtn) {
      nextBtn.addEventListener("click", () => {
        this.nextStep();
      });
    }

    if (prevBtn) {
      prevBtn.addEventListener("click", () => {
        this.prevStep();
      });
    }

    if (closeBtn) {
      closeBtn.addEventListener("click", () => {
        this.endTutorial();
      });
    }

    // Restart tutorial button
    const restartBtn = document.getElementById("restart-tutorial-btn");
    if (restartBtn) {
      restartBtn.addEventListener("click", () => {
        this.restartTutorial();
      });
    }

    // Keyboard navigation
    document.addEventListener("keydown", (e) => {
      if (this.isActive) {
        switch (e.key) {
          case "Escape":
            this.endTutorial();
            break;
          case "ArrowRight":
          case "Enter":
            this.nextStep();
            break;
          case "ArrowLeft":
            this.prevStep();
            break;
        }
      }
    });
  }

  showWelcomeModal() {
    const modal = document.getElementById("tutorial-welcome-modal");
    if (modal) {
      modal.classList.add("show");
      document.body.style.overflow = "hidden";
    }
  }

  hideWelcomeModal() {
    const modal = document.getElementById("tutorial-welcome-modal");
    if (modal) {
      modal.classList.remove("show");
      document.body.style.overflow = "";
    }
  }

  startTutorial() {
    this.isActive = true;
    this.currentStep = 0;
    this.showOverlay();
    this.showStep(this.currentStep);
  }

  skipTutorial() {
    this.hideWelcomeModal();
    this.markTutorialCompleted();
  }

  showOverlay() {
    const overlay = document.getElementById("tutorial-overlay");
    if (overlay) {
      overlay.classList.add("show");
      document.body.style.overflow = "hidden";
    }
  }

  hideOverlay() {
    const overlay = document.getElementById("tutorial-overlay");
    if (overlay) {
      overlay.classList.remove("show");
      document.body.style.overflow = "";
    }
  }

  showStep(stepIndex) {
    if (stepIndex < 0 || stepIndex >= this.steps.length) return;

    const step = this.steps[stepIndex];
    const target = document.querySelector(step.target);

    if (!target) {
      console.warn(`Tutorial target not found: ${step.target}`);
      return;
    }

    // Update step counter
    this.updateStepCounter();

    // Update tooltip content with translations
    this.updateTooltipContentWithTranslations(stepIndex);

    // Position spotlight and tooltip
    this.positionSpotlight(target);
    this.positionTooltip(target, step.position);

    // Update navigation buttons
    this.updateNavigationButtons();

    // Scroll target into view
    target.scrollIntoView({ behavior: "smooth", block: "center" });
  }

  updateTooltipContentWithTranslations(stepIndex) {
    const translations = this.getTranslations();
    const stepKey = `step${stepIndex + 1}`;

    if (
      translations &&
      translations.tutorial &&
      translations.tutorial[stepKey]
    ) {
      const stepTranslation = translations.tutorial[stepKey];
      const titleEl = document.getElementById("tutorial-tooltip-title");
      const textEl = document.getElementById("tutorial-tooltip-text");

      if (titleEl) titleEl.textContent = stepTranslation.title;
      if (textEl) textEl.textContent = stepTranslation.text;
    } else {
      // Fallback to hardcoded step data
      const step = this.steps[stepIndex];
      this.updateTooltipContent(step);
    }
  }

  updateStepCounter() {
    const currentStepEl = document.getElementById("tutorial-current-step");
    const totalStepsEl = document.getElementById("tutorial-total-steps");
    const stepCounterContainer = document.querySelector(
      ".tutorial-step-counter"
    );
    const translations = this.getTranslations();
    const isRTL = this.isRTLLanguage();

    if (currentStepEl) currentStepEl.textContent = this.currentStep + 1;
    if (totalStepsEl) totalStepsEl.textContent = this.steps.length;

    // Fix RTL step counter format
    if (stepCounterContainer && translations && translations.tutorial) {
      const currentStep = this.currentStep + 1;
      const totalSteps = this.steps.length;
      const stepCounter = translations.tutorial.stepCounter;

      if (isRTL) {
        // For RTL languages: "x من 9" (x of 9)
        stepCounterContainer.innerHTML = `${currentStep} ${stepCounter} ${totalSteps}`;
      } else {
        // For LTR languages: "x of 9"
        stepCounterContainer.innerHTML = `${currentStep} ${stepCounter} ${totalSteps}`;
      }
    }
  }

  updateTooltipContent(step) {
    const titleEl = document.getElementById("tutorial-tooltip-title");
    const textEl = document.getElementById("tutorial-tooltip-text");

    if (titleEl) titleEl.textContent = step.title;
    if (textEl) textEl.textContent = step.text;
  }

  positionSpotlight(target) {
    const spotlight = document.getElementById("tutorial-spotlight");
    if (!spotlight) return;

    // For body target (general tips), hide spotlight
    if (target.tagName === "BODY") {
      spotlight.style.display = "none";
      return;
    } else {
      spotlight.style.display = "block";
    }

    const rect = target.getBoundingClientRect();
    const padding = 10;

    spotlight.style.left = `${rect.left - padding}px`;
    spotlight.style.top = `${rect.top - padding}px`;
    spotlight.style.width = `${rect.width + padding * 2}px`;
    spotlight.style.height = `${rect.height + padding * 2}px`;
  }

  positionTooltip(target, position) {
    const tooltip = document.getElementById("tutorial-tooltip");
    if (!tooltip) return;

    const rect = target.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();
    const padding = 20;

    // Remove existing arrow classes
    tooltip.classList.remove(
      "arrow-top",
      "arrow-bottom",
      "arrow-left",
      "arrow-right"
    );

    let left, top;

    switch (position) {
      case "center":
        // Center the tooltip on screen for general tips
        left = window.innerWidth / 2 - tooltipRect.width / 2;
        top = window.innerHeight / 2 - tooltipRect.height / 2;
        // No arrow for center position
        break;
      case "top":
        left = rect.left + rect.width / 2 - tooltipRect.width / 2;
        top = rect.top - tooltipRect.height - padding;
        tooltip.classList.add("arrow-bottom");
        break;
      case "bottom":
        left = rect.left + rect.width / 2 - tooltipRect.width / 2;
        top = rect.bottom + padding;
        tooltip.classList.add("arrow-top");
        break;
      case "left":
        left = rect.left - tooltipRect.width - padding;
        top = rect.top + rect.height / 2 - tooltipRect.height / 2;
        tooltip.classList.add("arrow-right");
        break;
      case "right":
        left = rect.right + padding;
        top = rect.top + rect.height / 2 - tooltipRect.height / 2;
        tooltip.classList.add("arrow-left");
        break;
      default:
        left = rect.left + rect.width / 2 - tooltipRect.width / 2;
        top = rect.bottom + padding;
        tooltip.classList.add("arrow-top");
    }

    // Ensure tooltip stays within viewport with better mobile handling
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const isMobile = viewportWidth <= 768;

    if (isMobile) {
      // On mobile, center the tooltip horizontally and position it optimally
      left = (viewportWidth - tooltipRect.width) / 2;

      // Ensure minimum margins on mobile
      const mobileMargin = 16;
      if (left < mobileMargin) left = mobileMargin;
      if (left + tooltipRect.width > viewportWidth - mobileMargin) {
        left = viewportWidth - tooltipRect.width - mobileMargin;
      }

      // For mobile, prefer positioning at bottom of screen if space allows
      if (
        position !== "center" &&
        top + tooltipRect.height > viewportHeight - 100
      ) {
        top = viewportHeight - tooltipRect.height - 20;
      }
    } else {
      // Desktop positioning
      if (left < 10) left = 10;
      if (left + tooltipRect.width > viewportWidth - 10) {
        left = viewportWidth - tooltipRect.width - 10;
      }
    }

    // Common viewport constraints
    if (top < 10) top = 10;
    if (top + tooltipRect.height > viewportHeight - 10) {
      top = viewportHeight - tooltipRect.height - 10;
    }

    tooltip.style.left = `${left}px`;
    tooltip.style.top = `${top}px`;
  }

  updateNavigationButtons() {
    const prevBtn = document.getElementById("tutorial-prev-btn");
    const nextBtn = document.getElementById("tutorial-next-btn");
    const translations = this.getTranslations();
    const isRTL = this.isRTLLanguage();

    if (prevBtn) {
      prevBtn.style.display = this.currentStep > 0 ? "flex" : "none";
      if (translations && translations.tutorial) {
        // Use appropriate arrow direction for RTL languages
        const prevArrow = isRTL ? "fa-arrow-right" : "fa-arrow-left";
        prevBtn.innerHTML = `<i class="fas ${prevArrow}"></i> ${translations.tutorial.previous}`;
      }
    }

    if (nextBtn) {
      const isLastStep = this.currentStep >= this.steps.length - 1;
      if (translations && translations.tutorial) {
        if (isLastStep) {
          nextBtn.innerHTML = `<i class="fas fa-check"></i> ${translations.tutorial.finish}`;
        } else {
          // Use appropriate arrow direction for RTL languages
          const nextArrow = isRTL ? "fa-arrow-left" : "fa-arrow-right";
          nextBtn.innerHTML = `${translations.tutorial.next} <i class="fas ${nextArrow}"></i>`;
        }
      } else {
        // Fallback to English
        nextBtn.innerHTML = isLastStep
          ? '<i class="fas fa-check"></i> Finish'
          : 'Next <i class="fas fa-arrow-right"></i>';
      }
    }
  }

  isRTLLanguage() {
    const translations = this.getTranslations();
    return translations && translations.dir === "rtl";
  }

  nextStep() {
    if (this.currentStep < this.steps.length - 1) {
      this.currentStep++;
      this.showStep(this.currentStep);
    } else {
      this.endTutorial();
    }
  }

  prevStep() {
    if (this.currentStep > 0) {
      this.currentStep--;
      this.showStep(this.currentStep);
    }
  }

  endTutorial() {
    this.isActive = false;
    this.hideOverlay();
    this.markTutorialCompleted();
  }

  markTutorialCompleted() {
    localStorage.setItem("scTimer_tutorial_completed", "true");
    this.hasSeenTutorial = true;
  }

  // Public method to restart tutorial
  restartTutorial() {
    localStorage.removeItem("scTimer_tutorial_completed");
    this.hasSeenTutorial = false;
    this.showWelcomeModal();
  }
}

// Initialize tutorial manager when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.tutorialManager = new TutorialManager();
});

// Export for potential external use
if (typeof module !== "undefined" && module.exports) {
  module.exports = TutorialManager;
}
